let selectedItems = new Set();
let customItems = [];
let isEventListenerAttached = false;

async function initFaultEntryPage() {
    const faultEntryContent = document.getElementById('fault-entry-content');
    if (!faultEntryContent) {
        Logger.error('无法找到 fault-entry-content 元素');
        return;
    }
    
    faultEntryContent.innerHTML = `
        <div class="container">
            <!-- <h1>故障品录入</h1> -->
            <form id="faultEntryForm" novalidate>
                ${createBasicInfoForm()}
                
                <!-- 添加项目记录部分 -->
                <div class="project-record">
                    <div class="grid">
                        <div class="card">
                            <h2 class="card-title">固定测试项目</h2>
                            <input type="text" id="search" placeholder="搜索固定测试项目..." class="input">
                            <div class="scroll-area" id="fixed-items"></div>
                        </div>

                        <div class="card">
                            <h2 class="card-title">自定义测试项目</h2>
                            <div class="flex">
                                <input type="text" id="new-item" placeholder="添加新的测试项目..." class="input">
                                <button type="button" class="button" id="add-button" style="white-space: nowrap;">添加</button>
                            </div>
                            <div class="scroll-area" id="custom-items"></div>
                        </div>
                    </div>
                </div>
                
                <div class="button-group">
                    <button type="button" class="read-button" onclick="handleReadNetworkInfo()">读取网络信息</button>
                    <button type="button" class="submit-button" onclick="handleSubmit()">提交故障品信息 (已选择：0)</button>
                </div>
            </form>
        </div>
    `;

    // 获取表单元素（只声明一次）
    const form = document.getElementById('faultEntryForm');

    // 添加表单输入框的回车键处理
    form.addEventListener('keypress', function(e) {
        if (e.key === 'Enter') {
            e.preventDefault(); // 阻止默认的回车提交行为
            return false;
        }
    });

    // 初始化项目记录功能
    initProjectRecord();

    // 在页面初始化完成后获取并填充当前用户
    try {
        const response = await fetch('/api/fault-entry/get-current-user');
        const data = await response.json();
        
        if (data.success && data.username) {
            const testerInput = document.getElementById('tester');
            if (testerInput) {
                testerInput.value = data.username;
            }
        }
    } catch (error) {
        Logger.error('获取用户信息失败:', error);
    }
    
    // 添加工单号输入框的事件监听
    const orderNumberInput = document.getElementById('orderNumber');
    if (orderNumberInput) {
        orderNumberInput.addEventListener('input', async function() {
            const orderNumber = this.value.trim();
            
            // 清空相关字段
            const fieldsToReset = ['productionQuantity', 'productCode', 'productModel', 'batchNumber', 'snByteCount'];
            fieldsToReset.forEach(fieldId => {
                const element = document.getElementById(fieldId);
                if (element) {
                    element.value = '';
                    element.classList.remove('input-has-value');
                }
            });

            if (!orderNumber) return;

            try {
                const response = await fetch(`/api/work-order/by-number?orderNumber=${encodeURIComponent(orderNumber)}`);
                const data = await response.json();

                if (data.success && data.order) {
                    // 自动填充字段
                    const fieldsToFill = {
                        'productionQuantity': data.order.ord_productionQuantity,
                        'productCode': data.order.ord_productCode,
                        'productModel': data.order.ord_productModel,
                        'batchNumber': data.order.ord_probatch,
                        'snByteCount': data.order.ord_snlenth
                    };

                    // 填充字段并添加绿色背景
                    for (const [fieldId, value] of Object.entries(fieldsToFill)) {
                        const element = document.getElementById(fieldId);
                        if (element && value) {
                            element.value = value;
                            element.classList.add('input-has-value');
                        }
                    }

                    // 工单号有效，添加绿色背景
                    this.classList.add('input-has-value');
                } else {
                    // 工单号无效，移除绿色背景
                    this.classList.remove('input-has-value');
                }
            } catch (error) {
                Logger.error('获取工单信息失败:', error);
                this.classList.remove('input-has-value');
            }
        });
    }
    
    // 找到产品编码输入框并添加事件监听
    const productCodeInput = document.getElementById('productCode');
    if (productCodeInput) {
        productCodeInput.addEventListener('input', async function() {
            const code = this.value.trim();
            if (code) {
                try {
                    const response = await fetch(`/api/product-mapping/get-model?code=${encodeURIComponent(code)}`);
                    const data = await response.json();
                    
                    if (data.success && data.model) {
                        // 自动填充产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = data.model;
                            modelInput.classList.add('input-has-value');
                        }
                        // 产品编码有效，添加绿色背景
                        this.classList.add('input-has-value');
                    } else {
                        // 产品编码无效，移除绿色背景
                        this.classList.remove('input-has-value');
                        // 清空产品型号
                        const modelInput = document.getElementById('productModel');
                        if (modelInput) {
                            modelInput.value = '';
                            modelInput.classList.remove('input-has-value');
                        }
                    }
                } catch (error) {
                    Logger.error('获取产品型号失败:', error);
                }
            } else {
                // 输入框为空时移除绿色背景
                this.classList.remove('input-has-value');
                // 清空产品型号
                const modelInput = document.getElementById('productModel');
                if (modelInput) {
                    modelInput.value = '';
                    modelInput.classList.remove('input-has-value');
                }
            }
        });
    }

    // 添加消息提示函数（如果还没有的话）
    function showMessage(message, type = 'info', persistent = false, duration = 3000) {
        // 确保消息容器存在
        let messageContainer = document.querySelector('.message-container');
        if (!messageContainer) {
            messageContainer = document.createElement('div');
            messageContainer.className = 'message-container';
            document.body.appendChild(messageContainer);
        }

        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `message ${type}`;
        messageDiv.textContent = message;

        // 如果是持续显示的消息，添加关闭按钮
        if (persistent) {
            const closeButton = document.createElement('button');
            closeButton.className = 'close-message';
            closeButton.innerHTML = '×';
            closeButton.onclick = () => messageDiv.remove();
            messageDiv.appendChild(closeButton);
        } else {
            // 自动消失
            setTimeout(() => messageDiv.remove(), duration);
        }

        // 添加到消息容器
        messageContainer.appendChild(messageDiv);

        // 移除之前的相同类型消息
        const existingMessages = messageContainer.querySelectorAll(`.message.${type}`);
        existingMessages.forEach(msg => {
            if (msg !== messageDiv) {
                msg.remove();
            }
        });
    }
}

// 添加项目记录相关的功能
function initProjectRecord() {
    const fixedItems = [
        { name: "烧录异常", code: "burn_error" },
        { name: "上电异常", code: "power_error" },
        { name: "RS485_1通信异常", code: "rs485_1_error" },
        { name: "RS485_2通信异常", code: "rs485_2_error" },
        { name: "RS232通信异常", code: "rs232_error" },
        { name: "CANbus通信异常", code: "canbus_error" },
        { name: "EtherCAT通信异常", code: "ethercat_error" },
        { name: "Backplane Bus通信异常", code: "backplane_error" },
        { name: "Body I/O输入输出异常", code: "body_io_error" },
        { name: "Led数码管异常", code: "led_tube_error" },
        { name: "Led灯珠异常", code: "led_bulb_error" },
        { name: "U盘接口异常", code: "usb_error" },
        { name: "SD卡卡槽异常", code: "sd_slot_error" },
        { name: "调试串口异常", code: "debug_port_error" },
        { name: "网口异常", code: "ethernet_error" },
        { name: "拨码开关异常", code: "dip_switch_error" },
        { name: "复位按钮异常", code: "reset_btn_error" }
    ];
    
    const searchInput = document.getElementById('search');
    const newItemInput = document.getElementById('new-item');
    const addButton = document.getElementById('add-button');
    const fixedItemsContainer = document.getElementById('fixed-items');
    const customItemsContainer = document.getElementById('custom-items');
    const submitButton = document.querySelector('.submit-button');

    // 将 updateFixedItems, updateCustomItems, updateSubmitButton 定义为全局函数
    window.updateFixedItems = function(searchTerm = '') {
        const filtered = fixedItems.filter(item => 
            item.name.toLowerCase().includes(searchTerm.toLowerCase())
        );
        
        fixedItemsContainer.innerHTML = filtered.map((item, index) => `
            <div class="item">
                <input type="checkbox" id="fixed-${index}" 
                       data-code="${item.code}"
                       ${selectedItems.has(item.code) ? 'checked' : ''}
                       onchange="toggleItem('${item.code}', '${item.name}')">
                <label for="fixed-${index}">${item.name}</label>
            </div>
        `).join('');
    };

    window.updateCustomItems = function() {
        customItemsContainer.innerHTML = customItems.map((item, index) => `
            <div class="custom-item">
                <div class="item">
                    <input type="checkbox" id="custom-${index}"
                           data-code="custom_${index}"
                           ${selectedItems.has(`custom_${index}`) ? 'checked' : ''}
                           onchange="toggleItem('custom_${index}', '${item}')">
                    <label for="custom-${index}">${item}</label>
                </div>
                <button type="button" class="button button-delete" onclick="removeCustomItem('custom_${index}', '${item}')">删除</button>
            </div>
        `).join('');
    };

    window.updateSubmitButton = function() {
        submitButton.textContent = `提交故障品信息 (已选择：${selectedItems.size})`;
    };

    // 将 toggleItem 和 removeCustomItem 添加到全局作用域
    window.toggleItem = function(code, name) {
        if (selectedItems.has(code)) {
            selectedItems.delete(code);
        } else {
            selectedItems.add(code);
        }
        updateSubmitButton();
    };

    window.removeCustomItem = async function(code, item) {
        const result = await SweetAlert.confirm(
            `确定要删除故障项目 "${item}" 吗？`,
            '删除确认'
        );
        if (!result) return;
        
        customItems = customItems.filter(i => i !== item);
        selectedItems.delete(code);
        updateCustomItems();
        updateSubmitButton();
        SweetAlert.success('已删除故障项目');
    };

    function addCustomItem() {
        const newItem = newItemInput.value.trim();
        if (newItem && !customItems.includes(newItem)) {
            const customCode = `custom_${customItems.length}`;
            customItems.push(newItem);
            selectedItems.add(customCode); // 默认选中新添加的项目
            newItemInput.value = '';
            updateCustomItems();
            updateSubmitButton();
            SweetAlert.success('已添加自定义故障项目');
        } else if (customItems.includes(newItem)) {
            SweetAlert.warning('该故障项目已存在');
        }
    }

    // 事件监听器
    newItemInput.addEventListener('keypress', (e) => {
        if (e.key === 'Enter') {
            e.preventDefault(); // 防止表提交
            addCustomItem();
        }
    });

    searchInput.addEventListener('input', () => updateFixedItems(searchInput.value));
    addButton.addEventListener('click', addCustomItem);

    // 初始渲染
    window.updateFixedItems();
    window.updateCustomItems();
    window.updateSubmitButton();
}

// 将原来的提交处理逻辑移到单独的函数中
async function handleSubmit() {
    // 验证必填字段
    const requiredFields = {
        'tester': '测试人员',
        'orderNumber': '加工单号',
        'productionQuantity': '生产数量',
        'productCode': '产品编码',
        'productModel': '产品型号',
        'productStatus': '产品状态',
        'productSN': '产品SN'
    };

    // 检查所有必填字段
    for (const [fieldId, fieldName] of Object.entries(requiredFields)) {
        const field = document.getElementById(fieldId);
        const value = field.value.trim();
        
        if (!value) {
            await SweetAlert.warning(`${fieldName}不能为空！`);
            field.focus();
            return;
        }
    }

    // 检查已选择的故障项目数量
    const totalSelectedItems = selectedItems.size + customItems.length;
    if (totalSelectedItems === 0) {
        await SweetAlert.warning('请至少选择一个故障项目！');
        return;
    }

    // 先获取并打印产品状态值，用于调试
    const productStatus = document.getElementById('productStatus').value;
    Logger.log('产品状态值:', productStatus);
    
    // 收集表单数据
    const formData = {
        // 基本信息
        tester: document.getElementById('tester').value,
        test_time: document.getElementById('testTime').value,
        work_order: document.getElementById('orderNumber').value,
        work_qty: document.getElementById('productionQuantity').value,
        pro_model: document.getElementById('productModel').value,
        pro_code: document.getElementById('productCode').value,
        pro_sn: document.getElementById('productSN').value || 'N/A',
        pro_batch: document.getElementById('batchNumber').value || 'N/A',
        remarks: document.getElementById('remarks').value || 'N/A',
        
        // 收集选中的固定测试项目
        selected_items: Array.from(selectedItems),
        
        // 收集自定义测试项目
        custom_items: customItems
    };

    // 修改产品状态映射
    const productStatusMap = {
        'new': 1,      // 新品
        'used': 2,     // 维修
        'refurbished': 3  // 返工
    };
    
    // 设置产品状态
    formData.pro_status = productStatusMap[productStatus];
    Logger.log('转换后的产品状态值:', formData.pro_status);
    
    if (!formData.pro_status) {
        Logger.log('产品状态映射失败');
        await SweetAlert.warning('请选择产品状态！');
        return;
    }

    // 在提交前打印数据，用于调试
    Logger.log('提交的数据：', formData);
    
    try {
        const response = await fetch('/api/fault-entry/submit', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(formData)
        });
        
        const result = await response.json();
        
        if (result.success) {
            await SweetAlert.success('故障品信息提交成功！');
            
            // 保存需要保留的字段值
            const savedValues = {
                tester: document.getElementById('tester').value,
                orderNumber: document.getElementById('orderNumber').value,
                productionQuantity: document.getElementById('productionQuantity').value,
                productCode: document.getElementById('productCode').value,    // 添加产品编码
                productModel: document.getElementById('productModel').value,
                productStatus: document.getElementById('productStatus').value,
                batchNumber: document.getElementById('batchNumber').value,
                remarks: document.getElementById('remarks').value     // 添加备注
            };
            
            // 重置表单
            document.getElementById('faultEntryForm').reset();
            
            // 恢复保存的值
            document.getElementById('tester').value = savedValues.tester;
            document.getElementById('orderNumber').value = savedValues.orderNumber;
            document.getElementById('productionQuantity').value = savedValues.productionQuantity;
            document.getElementById('productCode').value = savedValues.productCode;
            document.getElementById('productModel').value = savedValues.productModel;
            document.getElementById('productStatus').value = savedValues.productStatus;
            document.getElementById('batchNumber').value = savedValues.batchNumber;
            document.getElementById('remarks').value = savedValues.remarks;
            
            // 清空选中的项目
            selectedItems.clear();
            customItems = [];
            updateFixedItems();
            updateCustomItems();
            updateSubmitButton();
            
        } else {
            await SweetAlert.error(result.message);
        }
    } catch (error) {
        await SweetAlert.error('请检查网络连接');
        Logger.error('提交错误：', error);
    }
}

// 读取网络信息功能
async function handleReadNetworkInfo() {
    try {
        // 显示加载状态
        const readButton = document.querySelector('.read-button');
        const originalText = readButton.textContent;
        readButton.textContent = '读取中...';
        readButton.disabled = true;

        // 调用后端API来触发Python程序
        const response = await fetch('/api/fault-entry/read-network-info', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            }
        });

        const result = await response.json();

        if (result.success) {
            // 显示读取到的网络信息
            const networkInfo = result.data;
            const infoText = `
网络信息读取成功：
IP地址: ${networkInfo.eth0ipV4}
子网掩码: ${networkInfo.eth0netmask}
网关: ${networkInfo.eth0gate}
MAC地址: ${networkInfo.eth0mac}
            `;

            await SweetAlert.success(infoText);

            // 可以选择将信息填入备注字段
            const remarksField = document.getElementById('remarks');
            if (remarksField) {
                const currentRemarks = remarksField.value;
                const networkInfoText = `网络信息 - IP:${networkInfo.eth0ipV4}, MAC:${networkInfo.eth0mac}`;
                remarksField.value = currentRemarks ? `${currentRemarks}\n${networkInfoText}` : networkInfoText;
            }

        } else {
            // 根据错误类型显示不同的提示
            const errorMessage = result.message || '读取网络信息失败';

            if (errorMessage.includes('无法连接到本地程序')) {
                await SweetAlert.error(`
                    ${errorMessage}

                    解决方法：
                    1. 双击运行 "启动本地网络读取服务.bat"
                    2. 或者在命令行运行: python services/network_reader.py
                    3. 确保看到 "Socket服务器启动成功" 提示
                `);
            } else if (errorMessage.includes('连接本地程序超时')) {
                await SweetAlert.error(`
                    ${errorMessage}

                    可能原因：
                    1. 本地服务程序未启动
                    2. 端口9999被占用
                    3. 防火墙阻止了连接
                `);
            } else {
                await SweetAlert.error(errorMessage);
            }
        }
    } catch (error) {
        Logger.error('读取网络信息错误：', error);
        await SweetAlert.error('网络连接失败，请检查连接');
    } finally {
        // 恢复按钮状态
        const readButton = document.querySelector('.read-button');
        readButton.textContent = originalText;
        readButton.disabled = false;
    }
}
