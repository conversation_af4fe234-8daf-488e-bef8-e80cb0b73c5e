# 网络信息读取功能实现说明

## 功能概述

在故障录入页面添加了一个"读取"按钮，点击该按钮可以执行本地Python程序来获取指定网络设备的配置信息，并将结果显示给用户。

## 实现架构

```
前端页面 (FaultEntry.js) 
    ↓ HTTP POST请求
后端API (/api/fault-entry/read-network-info)
    ↓ subprocess调用
Python脚本 (services/network_reader.py)
    ↓ HTTP请求
网络设备 (*************:8090)
```

## 文件修改清单

### 1. 前端修改

**文件**: `static/page_js_css/FaultEntry.js`

- 添加了"读取"按钮到页面布局
- 实现了`handleReadNetworkInfo()`函数来处理按钮点击
- 添加了加载状态显示和错误处理
- 将读取到的网络信息自动填入备注字段

**文件**: `static/style.css`

- 添加了`.button-group`样式用于按钮布局
- 添加了`.read-button`样式，包括hover和disabled状态
- 优化了`.submit-button`样式

### 2. 后端修改

**文件**: `routes/fault_entry.py`

- 添加了`/read-network-info` API端点
- 实现了通过subprocess调用Python脚本的逻辑
- 添加了错误处理和超时控制

**文件**: `app.py`

- 添加了测试页面路由（可选）

### 3. 新增文件

**文件**: `services/network_reader.py`

- 实现了`NetworkInfoReader`类来获取网络设备信息
- 支持HTTP Basic认证
- 包含完整的错误处理和日志记录
- 可以独立运行进行测试

**文件**: `test_network_reader.html`

- 独立的测试页面，用于验证功能
- 包含完整的UI和JavaScript代码

**文件**: `test_api.py`

- API测试脚本，用于验证后端功能

## 配置信息

### 网络设备配置

```python
url = "http://*************:8090/cgi-bin/netinfo?key=eth0"
username = "admin"
password = "admin"
```

### 预期响应格式

```json
{
  "error": false,
  "data": {
    "eth0": {
      "eth0ipV4": "*************",
      "eth0netmask": "*************",
      "eth0gate": "**************",
      "eth0mac": "00:4C:0E:22:94:D4"
    }
  }
}
```

## 使用方法

1. 打开故障录入页面
2. 点击"读取网络信息"按钮
3. 系统会自动调用Python程序获取网络设备信息
4. 成功后会显示网络信息，并自动填入备注字段
5. 如果失败会显示相应的错误信息

## 错误处理

- **连接超时**: 30秒超时限制
- **设备无响应**: 显示连接错误信息
- **认证失败**: 显示HTTP状态码错误
- **数据格式错误**: JSON解析错误处理
- **脚本执行失败**: subprocess错误处理

## 测试方法

### 1. 独立测试Python脚本

```bash
python services/network_reader.py
```

### 2. 测试API端点

```bash
python test_api.py
```

### 3. 前端功能测试

访问: `http://127.0.0.1:3308/test_network_reader.html`

## 安全考虑

- 网络设备的用户名和密码硬编码在脚本中
- 建议在生产环境中使用配置文件或环境变量
- HTTP请求忽略了SSL证书验证（适用于内网设备）

## 扩展性

该架构支持以下扩展：

1. **多设备支持**: 可以修改脚本支持多个网络设备
2. **配置化**: 可以将设备信息移到配置文件中
3. **缓存机制**: 可以添加结果缓存避免频繁请求
4. **异步处理**: 可以改为异步处理提高响应速度

## 依赖项

- `requests`: HTTP请求库
- `subprocess`: Python标准库
- `json`: Python标准库
- `logging`: Python标准库

## 注意事项

1. 确保网络设备可访问
2. 确保设备认证信息正确
3. 防火墙需要允许相关端口访问
4. 建议在内网环境中使用
