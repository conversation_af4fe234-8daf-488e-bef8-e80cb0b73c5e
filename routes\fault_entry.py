from flask import Blueprint, jsonify, request, current_app
from jwt import decode
from functools import wraps
from database.db_manager import DatabaseManager
from datetime import datetime
from sqlalchemy import text
import subprocess
import json
import os
import sys

fault_entry_bp = Blueprint('fault_entry', __name__)

def get_current_user():
    token = request.cookies.get('token')
    if token:
        try:
            payload = decode(token, current_app.config['JWT_SECRET'], algorithms=["HS256"])
            return payload.get('username')
        except:
            return None
    return None

@fault_entry_bp.route('/get-current-user', methods=['GET'])
def get_current_user_route():
    username = get_current_user()
    return jsonify({
        'success': True,
        'username': username
    })

@fault_entry_bp.route('/submit', methods=['POST'])
def submit_fault():
    try:
        data = request.get_json()
        print("Received data:", data)
        db = DatabaseManager()
        
        pro_status = data.get('pro_status')
        if pro_status is None:
            return jsonify({
                'success': False,
                'message': '产品状态不能为空'
            }), 400

        with db.get_session() as session:
            # 首先检查 SN 号是否存在（如果提供了SN号）
            if data.get('pro_sn') and data['pro_sn'] != 'N/A':
                check_sql = text("SELECT pro_status, maintenance, rework FROM faultEntry_table WHERE pro_sn = :pro_sn")
                result = session.execute(check_sql, {'pro_sn': data['pro_sn']}).fetchone()

                if result:
                    # SN号已存在
                    if pro_status == 1:  # 新品
                        return jsonify({
                            'success': False,
                            'message': f'产品SN号 {data["pro_sn"]} 已存在，新品不能使用重复的SN号'
                        }), 400
                    
                    # 获取现有的维修和返工次数
                    existing_maintenance = result[1] or 0
                    existing_rework = result[2] or 0
                    
                    # 更新维修或返工次数
                    if pro_status == 2:  # 维修
                        maintenance = existing_maintenance + 1
                        rework = existing_rework
                    else:  # 返工
                        maintenance = existing_maintenance
                        rework = existing_rework + 1
                else:
                    maintenance = 0
                    rework = 0
            else:
                maintenance = 0
                rework = 0

            # 准备错误字段参数
            error_params = {
                'burn_err': 2 if 'burn_error' in data['selected_items'] else 0,
                'power_err': 2 if 'power_error' in data['selected_items'] else 0,
                'backplane_err': 2 if 'backplane_error' in data['selected_items'] else 0,
                'body_io_err': 2 if 'body_io_error' in data['selected_items'] else 0,
                'led_tube_err': 2 if 'led_tube_error' in data['selected_items'] else 0,
                'led_bulb_err': 2 if 'led_bulb_error' in data['selected_items'] else 0,
                'net_port_err': 2 if 'ethernet_error' in data['selected_items'] else 0,
                'rs485_1_err': 2 if 'rs485_1_error' in data['selected_items'] else 0,
                'rs485_2_err': 2 if 'rs485_2_error' in data['selected_items'] else 0,
                'rs232_err': 2 if 'rs232_error' in data['selected_items'] else 0,
                'canbus_err': 2 if 'canbus_error' in data['selected_items'] else 0,
                'ethercat_err': 2 if 'ethercat_error' in data['selected_items'] else 0,
                'usb_drive_err': 2 if 'usb_error' in data['selected_items'] else 0,
                'sd_slot_err': 2 if 'sd_slot_error' in data['selected_items'] else 0,
                'debug_port_err': 2 if 'debug_port_error' in data['selected_items'] else 0,
                'dip_switch_err': 2 if 'dip_switch_error' in data['selected_items'] else 0,
                'reset_btn_err': 2 if 'reset_btn_error' in data['selected_items'] else 0,
                'other_err': ', '.join(data.get('custom_items', [])) or 'N/A'
            }

            # 准备基本参数
            params = {
                'tester': data['tester'],
                'work_order': data['work_order'],
                'work_qty': int(data['work_qty']),
                'pro_model': data['pro_model'],
                'pro_status': pro_status,
                'pro_sn': data.get('pro_sn', 'N/A'),
                'pro_batch': data.get('pro_batch', 'N/A'),
                'remarks': data.get('remarks', 'N/A'),
                'rework': rework,
                'maintenance': maintenance,
                'pro_code': data.get('pro_code', 'N/A'),
                **error_params  # 展开错误字段参数
            }

            # 执行插入
            insert_sql = text("""
                INSERT INTO faultEntry_table (
                    tester, work_order, work_qty, pro_model, pro_status,
                    pro_sn, pro_batch, remarks, rework, maintenance,
                    pro_code,
                    burn_err, power_err, backplane_err, body_io_err,
                    led_tube_err, led_bulb_err, net_port_err, rs485_1_err,
                    rs485_2_err, rs232_err, canbus_err, ethercat_err,
                    usb_drive_err, sd_slot_err, debug_port_err, dip_switch_err,
                    reset_btn_err, other_err
                ) VALUES (
                    :tester, :work_order, :work_qty, :pro_model, :pro_status,
                    :pro_sn, :pro_batch, :remarks, :rework, :maintenance,
                    :pro_code,
                    :burn_err, :power_err, :backplane_err, :body_io_err,
                    :led_tube_err, :led_bulb_err, :net_port_err, :rs485_1_err,
                    :rs485_2_err, :rs232_err, :canbus_err, :ethercat_err,
                    :usb_drive_err, :sd_slot_err, :debug_port_err, :dip_switch_err,
                    :reset_btn_err, :other_err
                )
            """)
            
            print("Parameters for SQL:", params)
            
            session.execute(insert_sql, params)
            session.commit()
            
            return jsonify({
                'success': True,
                'message': '故障品信息提交成功'
            })
        
    except Exception as e:
        print(f"Error details: {str(e)}")
        return jsonify({
            'success': False,
            'message': f'提交失败：{str(e)}'
        }), 500

@fault_entry_bp.route('/get-fault-list', methods=['GET'])
def get_fault_list():
    # 获取故障列表
    return jsonify({
        'status': 'success',
        'data': [
            {'id': 1, 'code': 'F001', 'description': '通信故障'},
            {'id': 2, 'code': 'F002', 'description': '硬件故障'}
        ]
    })

@fault_entry_bp.route('/read-network-info', methods=['POST'])
def read_network_info():
    """读取网络设备信息"""
    try:
        # 获取当前脚本目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(current_dir)
        script_path = os.path.join(project_root, 'services', 'network_reader.py')

        # 检查脚本是否存在
        if not os.path.exists(script_path):
            return jsonify({
                'success': False,
                'message': f'网络读取脚本不存在: {script_path}'
            }), 500

        # 执行Python脚本
        try:
            # 使用当前Python解释器执行脚本
            result = subprocess.run(
                [sys.executable, script_path],
                capture_output=True,
                text=True,
                timeout=30,  # 30秒超时
                cwd=project_root
            )

            if result.returncode == 0:
                # 解析输出
                try:
                    output_data = json.loads(result.stdout.strip())
                    if output_data.get('success'):
                        return jsonify({
                            'success': True,
                            'data': output_data['data']
                        })
                    else:
                        return jsonify({
                            'success': False,
                            'message': output_data.get('message', '读取失败')
                        })
                except json.JSONDecodeError:
                    return jsonify({
                        'success': False,
                        'message': f'脚本输出格式错误: {result.stdout}'
                    })
            else:
                return jsonify({
                    'success': False,
                    'message': f'脚本执行失败: {result.stderr}'
                })

        except subprocess.TimeoutExpired:
            return jsonify({
                'success': False,
                'message': '读取超时，请检查设备连接'
            })
        except Exception as e:
            return jsonify({
                'success': False,
                'message': f'执行脚本时发生错误: {str(e)}'
            })

    except Exception as e:
        print(f"读取网络信息错误: {e}")
        return jsonify({
            'success': False,
            'message': f'服务器错误: {str(e)}'
        }), 500
