# Socket网络读取功能使用说明

## 功能概述

通过Socket通信实现前端网页按钮调用本地Python程序，获取网络设备信息。这种方式实现了真正的本地程序调用，而不是作为后端API直接执行。

## 架构设计

```
前端网页 (FaultEntry.js)
    ↓ HTTP POST请求
后端API (/api/fault-entry/read-network-info)
    ↓ Socket通信 (localhost:9999)
本地Python程序 (services/network_reader.py)
    ↓ HTTP请求
网络设备 (*************:8090)
```

## 使用步骤

### 1. 启动本地网络读取服务

**方法一：使用批处理文件（推荐）**
```bash
双击运行: 启动本地网络读取服务.bat
```

**方法二：命令行启动**
```bash
python services/network_reader.py
```

启动成功后会看到：
```
正在启动网络信息读取服务...
Socket服务器将监听 localhost:9999
Socket服务器启动成功，监听 localhost:9999
```

### 2. 启动Flask Web应用

```bash
python app.py
```

### 3. 使用网页功能

1. 打开浏览器访问故障录入页面
2. 点击"读取网络信息"按钮
3. 系统会通过Socket调用本地程序
4. 成功后显示网络信息并填入备注字段

## 技术实现细节

### Socket通信协议

**请求格式：**
```
客户端发送: "READ_NETWORK_INFO"
```

**响应格式：**
```json
{
  "success": true,
  "data": {
    "eth0ipV4": "*************",
    "eth0netmask": "*************",
    "eth0gate": "**************",
    "eth0mac": "00:4c:0e:1b:67:92"
  }
}
```

### 错误处理

1. **连接被拒绝**：本地服务未启动
2. **连接超时**：网络问题或端口被占用
3. **数据格式错误**：JSON解析失败
4. **设备无响应**：目标网络设备不可达

## 文件说明

### 核心文件

1. **services/network_reader.py** - 本地Socket服务器程序
   - 监听端口：9999
   - 处理网络信息读取请求
   - 与网络设备通信

2. **routes/fault_entry.py** - 后端API
   - 提供 `/api/fault-entry/read-network-info` 端点
   - 通过Socket与本地程序通信

3. **static/page_js_css/FaultEntry.js** - 前端界面
   - 添加"读取网络信息"按钮
   - 处理用户交互和结果显示

### 辅助文件

1. **启动本地网络读取服务.bat** - 启动脚本
2. **测试Socket通信.py** - 测试工具

## 测试方法

### 1. 测试本地Socket服务

```bash
python 测试Socket通信.py
```

### 2. 手动测试Socket连接

```python
import socket
import json

client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('localhost', 9999))
client.send(b"READ_NETWORK_INFO")
response = client.recv(4096).decode('utf-8')
print(json.loads(response))
client.close()
```

## 配置说明

### 网络设备配置

在 `services/network_reader.py` 中修改：

```python
self.url = "http://*************:8090/cgi-bin/netinfo?key=eth0"
self.username = "admin"
self.password = "admin"
```

### Socket配置

在 `routes/fault_entry.py` 中修改：

```python
host = 'localhost'
port = 9999
timeout = 30
```

## 优势特点

1. **真正的本地程序调用**：不依赖后端服务器执行
2. **独立部署**：本地程序可以独立运行和维护
3. **实时通信**：通过Socket实现快速响应
4. **错误隔离**：本地程序崩溃不影响Web服务
5. **灵活扩展**：可以轻松添加更多本地功能

## 故障排除

### 常见问题

1. **"无法连接到本地程序"**
   - 检查本地服务是否启动
   - 确认端口9999未被占用
   - 检查防火墙设置

2. **"连接本地程序超时"**
   - 重启本地服务
   - 检查网络连接
   - 确认Socket配置正确

3. **"网络设备无响应"**
   - 检查设备IP地址和端口
   - 验证用户名密码
   - 确认设备网络连通性

### 调试方法

1. 查看本地服务日志输出
2. 使用测试脚本验证Socket通信
3. 检查浏览器开发者工具网络请求
4. 查看Flask应用日志

## 安全注意事项

1. Socket服务仅监听localhost，避免外部访问
2. 网络设备认证信息需要妥善保管
3. 建议在生产环境中使用配置文件管理敏感信息
4. 定期更新设备访问凭据

## 扩展建议

1. **多设备支持**：扩展协议支持多个设备查询
2. **配置管理**：使用配置文件管理设备信息
3. **日志记录**：增强日志记录和监控
4. **安全加固**：添加认证和加密机制
5. **异步处理**：使用异步Socket提高性能
