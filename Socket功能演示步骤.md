# Socket网络读取功能演示步骤

## 🎯 功能说明

实现了通过Socket通信让前端网页按钮调用本地Python程序的功能，真正做到了本地程序调用而非后端API执行。

## 📋 演示步骤

### 第一步：启动本地Socket服务器

**方法一：使用批处理文件（推荐）**
```
双击运行: 启动本地网络读取服务.bat
```

**方法二：命令行启动**
```bash
python services/network_reader.py
```

**预期输出：**
```
正在启动网络信息读取服务...
Socket服务器将监听 localhost:9999
按 Ctrl+C 停止服务
Socket服务器启动成功，监听 localhost:9999
```

### 第二步：启动Flask Web应用

```bash
python app.py
```

**预期输出：**
```
Starting application...
* Running on http://127.0.0.1:3308
```

### 第三步：访问故障录入页面

1. 打开浏览器
2. 访问：`http://127.0.0.1:3308`
3. 登录系统
4. 进入故障录入页面

### 第四步：测试网络读取功能

1. 在故障录入页面找到"读取网络信息"按钮（绿色按钮）
2. 点击按钮
3. 观察以下过程：
   - 按钮变为"读取中..."状态
   - 系统通过Socket调用本地程序
   - 本地程序获取网络设备信息
   - 返回结果并显示成功提示
   - 网络信息自动填入备注字段

## 🔍 技术验证

### 验证Socket通信

可以通过以下Python代码手动测试Socket连接：

```python
import socket
import json

# 连接到本地Socket服务器
client = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
client.connect(('localhost', 9999))

# 发送请求
client.send(b"READ_NETWORK_INFO")

# 接收响应
response = client.recv(4096).decode('utf-8')
data = json.loads(response)

print("网络信息:", data)
client.close()
```

### 验证API端点

```python
import requests

response = requests.post(
    'http://127.0.0.1:3308/api/fault-entry/read-network-info',
    json={}
)
print("API响应:", response.json())
```

## 📊 预期结果

### 成功情况

**网络信息显示：**
```
网络信息读取成功：
IP地址: *************
子网掩码: *************
网关: **************
MAC地址: 00:4c:0e:1b:67:92
```

**备注字段自动填入：**
```
网络信息 - IP:*************, MAC:00:4c:0e:1b:67:92
```

### 错误情况处理

1. **本地服务未启动**
   ```
   无法连接到本地程序，请先启动 services/network_reader.py
   
   解决方法：
   1. 双击运行 "启动本地网络读取服务.bat"
   2. 或者在命令行运行: python services/network_reader.py
   3. 确保看到 "Socket服务器启动成功" 提示
   ```

2. **连接超时**
   ```
   连接本地程序超时，请确保本地网络读取程序正在运行
   
   可能原因：
   1. 本地服务程序未启动
   2. 端口9999被占用
   3. 防火墙阻止了连接
   ```

## 🏗️ 架构优势

1. **真正的本地调用**：不依赖Web服务器执行业务逻辑
2. **进程隔离**：本地程序崩溃不影响Web应用
3. **独立部署**：可以单独更新和维护本地程序
4. **实时通信**：Socket通信响应快速
5. **扩展性强**：可以轻松添加更多本地功能

## 🔧 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   netstat -an | findstr :9999
   ```

2. **防火墙阻止**
   - 检查Windows防火墙设置
   - 确保允许Python程序网络访问

3. **权限问题**
   - 以管理员身份运行程序
   - 检查文件访问权限

### 调试方法

1. 查看Socket服务器控制台输出
2. 检查Flask应用日志
3. 使用浏览器开发者工具查看网络请求
4. 运行独立的Socket测试脚本

## 📝 文件清单

### 核心文件
- `services/network_reader.py` - Socket服务器程序
- `routes/fault_entry.py` - API端点
- `static/page_js_css/FaultEntry.js` - 前端界面
- `static/style.css` - 样式文件

### 辅助文件
- `启动本地网络读取服务.bat` - 启动脚本
- `Socket网络读取功能使用说明.md` - 详细说明
- `Socket功能演示步骤.md` - 本文档

## 🎉 演示完成

通过以上步骤，你可以完整演示Socket通信实现的本地程序调用功能。这种架构实现了真正的前端调用本地程序，而不是简单的后端API执行。
